export interface ImageMetadata {
  id: string;
  projectId: string;
  agentId: string;
  imageType: 'generated' | 'uploaded';
  filePath: string;
  fileName: string;
  planId?: string;
  description?: string;
  createdAt: string;
}

export interface ProjectImageStorage {
  generated: ImageMetadata[];
  uploaded: ImageMetadata[];
}

const STORAGE_KEY_PREFIX = 'mediapilot_project_images_';
const MAX_IMAGES_PER_TYPE = 20;

export class ProjectImageStorageManager {
  private getStorageKey(projectId: string): string {
    return `${STORAGE_KEY_PREFIX}${projectId}`;
  }

  private getProjectImages(projectId: string): ProjectImageStorage {
    try {
      const stored = localStorage.getItem(this.getStorageKey(projectId));
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error parsing stored project images:', error);
    }
    
    return {
      generated: [],
      uploaded: [],
    };
  }

  private saveProjectImages(projectId: string, images: ProjectImageStorage): void {
    try {
      localStorage.setItem(this.getStorageKey(projectId), JSON.stringify(images));
    } catch (error) {
      console.error('Error saving project images:', error);
    }
  }

  addGeneratedImage(
    projectId: string,
    agentId: string,
    filePath: string,
    fileName: string,
    planId?: string,
    description?: string
  ): void {
    const images = this.getProjectImages(projectId);
    
    const newImage: ImageMetadata = {
      id: `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      projectId,
      agentId,
      imageType: 'generated',
      filePath,
      fileName,
      planId,
      description,
      createdAt: new Date().toISOString(),
    };

    images.generated.unshift(newImage);
    
    // Keep only the most recent images
    if (images.generated.length > MAX_IMAGES_PER_TYPE) {
      images.generated = images.generated.slice(0, MAX_IMAGES_PER_TYPE);
    }

    this.saveProjectImages(projectId, images);
  }

  addUploadedImage(
    projectId: string,
    agentId: string,
    filePath: string,
    fileName: string,
    planId?: string
  ): void {
    const images = this.getProjectImages(projectId);
    
    const newImage: ImageMetadata = {
      id: `upl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      projectId,
      agentId,
      imageType: 'uploaded',
      filePath,
      fileName,
      planId,
      createdAt: new Date().toISOString(),
    };

    images.uploaded.unshift(newImage);
    
    // Keep only the most recent images
    if (images.uploaded.length > MAX_IMAGES_PER_TYPE) {
      images.uploaded = images.uploaded.slice(0, MAX_IMAGES_PER_TYPE);
    }

    this.saveProjectImages(projectId, images);
  }

  getRecentImages(projectId: string): ProjectImageStorage {
    return this.getProjectImages(projectId);
  }

  clearProjectImages(projectId: string): void {
    try {
      localStorage.removeItem(this.getStorageKey(projectId));
    } catch (error) {
      console.error('Error clearing project images:', error);
    }
  }

  clearAllProjectImages(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(STORAGE_KEY_PREFIX)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Error clearing all project images:', error);
    }
  }

  // Migration function to move old localStorage data to project-based structure
  migrateOldStorageToProject(projectId: string): void {
    try {
      const oldGeneratedKey = 'mediapilot_recent_generated_images';
      const oldUploadedKey = 'mediapilot_recent_uploaded_images';
      
      const oldGenerated = localStorage.getItem(oldGeneratedKey);
      const oldUploaded = localStorage.getItem(oldUploadedKey);
      
      if (oldGenerated || oldUploaded) {
        const images = this.getProjectImages(projectId);
        
        if (oldGenerated) {
          try {
            const parsedGenerated = JSON.parse(oldGenerated);
            if (Array.isArray(parsedGenerated)) {
              // Convert old format to new format
              const convertedGenerated = parsedGenerated.map((item: any, index: number) => ({
                id: `migrated_gen_${Date.now()}_${index}`,
                projectId,
                agentId: 'unknown',
                imageType: 'generated' as const,
                filePath: item.url || item.filePath || '',
                fileName: item.name || item.fileName || `Generated Image ${index + 1}`,
                description: item.description || item.prompt || '',
                createdAt: item.timestamp ? new Date(item.timestamp).toISOString() : new Date().toISOString(),
              }));
              images.generated = [...convertedGenerated, ...images.generated].slice(0, MAX_IMAGES_PER_TYPE);
            }
          } catch (error) {
            console.error('Error migrating old generated images:', error);
          }
        }
        
        if (oldUploaded) {
          try {
            const parsedUploaded = JSON.parse(oldUploaded);
            if (Array.isArray(parsedUploaded)) {
              // Convert old format to new format
              const convertedUploaded = parsedUploaded.map((item: any, index: number) => ({
                id: `migrated_upl_${Date.now()}_${index}`,
                projectId,
                agentId: 'unknown',
                imageType: 'uploaded' as const,
                filePath: item.url || item.filePath || '',
                fileName: item.name || item.fileName || `Uploaded Image ${index + 1}`,
                createdAt: item.timestamp ? new Date(item.timestamp).toISOString() : new Date().toISOString(),
              }));
              images.uploaded = [...convertedUploaded, ...images.uploaded].slice(0, MAX_IMAGES_PER_TYPE);
            }
          } catch (error) {
            console.error('Error migrating old uploaded images:', error);
          }
        }
        
        this.saveProjectImages(projectId, images);
        
        // Clean up old storage
        localStorage.removeItem(oldGeneratedKey);
        localStorage.removeItem(oldUploadedKey);
      }
    } catch (error) {
      console.error('Error during migration:', error);
    }
  }
}

// Export a singleton instance
export const projectImageStorage = new ProjectImageStorageManager();
